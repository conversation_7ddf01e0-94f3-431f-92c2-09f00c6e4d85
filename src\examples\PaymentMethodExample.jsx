import React from 'react';
import { Container, Row, Col, Card, Table, Badge } from 'react-bootstrap';
import { PaymentMethodService } from '../services/paymentMethodService';
import PaymentMethodBadge from '../components/PaymentMethodBadge';
import { 
  getPaymentMethodStatistics, 
  getUniquePaymentMethods,
  filterInvoicesByPaymentMethod,
  sortPaymentMethodsByPreference 
} from '../utils/paymentMethodUtils';

/**
 * Example component demonstrating the extracted payment method functionality
 */
const PaymentMethodExample = () => {
  // Sample invoice data for demonstration
  const sampleInvoices = [
    { id: 1, methode_paiement: 'carte', montant: 150.50 },
    { id: 2, methode_paiement: 'paypal', montant: 89.99 },
    { id: 3, methode_paiement: 'carte_bancaire', montant: 299.00 },
    { id: 4, methode_paiement: 'virement', montant: 450.75 },
    { id: 5, methode_paiement: 'carte', montant: 75.25 },
    { id: 6, methode_paiement: 'especes', montant: 25.00 }
  ];

  // Get payment method statistics
  const stats = getPaymentMethodStatistics(sampleInvoices);
  
  // Get unique payment methods
  const uniqueMethods = getUniquePaymentMethods(sampleInvoices);
  
  // Get all available payment methods
  const allMethods = PaymentMethodService.getAvailablePaymentMethods();
  
  // Sort methods by preference
  const sortedMethods = sortPaymentMethodsByPreference(uniqueMethods);

  return (
    <Container fluid className="py-4">
      <h2 className="mb-4">Payment Method Service Examples</h2>
      
      {/* Basic Usage */}
      <Row className="mb-4">
        <Col md={6}>
          <Card>
            <Card.Header>
              <h5>Basic Payment Method Display</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex flex-wrap gap-2">
                <PaymentMethodBadge methodCode="carte" />
                <PaymentMethodBadge methodCode="paypal" />
                <PaymentMethodBadge methodCode="virement" />
                <PaymentMethodBadge methodCode="especes" />
                <PaymentMethodBadge methodCode="unknown_method" />
              </div>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={6}>
          <Card>
            <Card.Header>
              <h5>Payment Method Information</h5>
            </Card.Header>
            <Card.Body>
              <Table size="sm">
                <thead>
                  <tr>
                    <th>Code</th>
                    <th>Display Name</th>
                    <th>Valid</th>
                  </tr>
                </thead>
                <tbody>
                  {['carte', 'paypal', 'invalid_method'].map(code => {
                    const method = PaymentMethodService.formatPaymentMethod(code);
                    return (
                      <tr key={code}>
                        <td><code>{code}</code></td>
                        <td>{method.displayName}</td>
                        <td>
                          <Badge bg={method.isValid ? 'success' : 'warning'}>
                            {method.isValid ? 'Valid' : 'Invalid'}
                          </Badge>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Statistics */}
      <Row className="mb-4">
        <Col md={6}>
          <Card>
            <Card.Header>
              <h5>Payment Method Statistics</h5>
            </Card.Header>
            <Card.Body>
              <Table size="sm">
                <thead>
                  <tr>
                    <th>Method</th>
                    <th>Count</th>
                    <th>Total Amount</th>
                    <th>Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.values(stats.byMethod).map(stat => (
                    <tr key={stat.code}>
                      <td>
                        <PaymentMethodBadge methodCode={stat.code} size={10} />
                      </td>
                      <td>{stat.count}</td>
                      <td>{stat.totalAmount.toFixed(2)} TND</td>
                      <td>{stat.percentage}%</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
              <small className="text-muted">
                Total: {stats.totalPayments} payments, {stats.methodCount} different methods
              </small>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={6}>
          <Card>
            <Card.Header>
              <h5>Available Payment Methods</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex flex-wrap gap-2">
                {allMethods.map(method => (
                  <PaymentMethodBadge 
                    key={method.code} 
                    methodCode={method.code} 
                    size={10}
                  />
                ))}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Filtering Example */}
      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header>
              <h5>Filtering by Payment Method</h5>
            </Card.Header>
            <Card.Body>
              {sortedMethods.map(method => {
                const filtered = filterInvoicesByPaymentMethod(sampleInvoices, method.code);
                return (
                  <div key={method.code} className="mb-2">
                    <PaymentMethodBadge methodCode={method.code} size={10} />
                    <span className="ms-2">
                      {filtered.length} invoice(s) - 
                      Total: {filtered.reduce((sum, inv) => sum + inv.montant, 0).toFixed(2)} TND
                    </span>
                  </div>
                );
              })}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Service Methods Demo */}
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h5>Service Methods Demo</h5>
            </Card.Header>
            <Card.Body>
              <Table size="sm">
                <thead>
                  <tr>
                    <th>Method</th>
                    <th>getDisplayName()</th>
                    <th>getBadgeVariant()</th>
                    <th>isValidPaymentMethod()</th>
                  </tr>
                </thead>
                <tbody>
                  {['carte', 'paypal', 'carte_bancaire', 'unknown'].map(code => (
                    <tr key={code}>
                      <td><code>{code}</code></td>
                      <td>{PaymentMethodService.getDisplayName(code)}</td>
                      <td>
                        <Badge bg={PaymentMethodService.getBadgeVariant(code)}>
                          {PaymentMethodService.getBadgeVariant(code)}
                        </Badge>
                      </td>
                      <td>
                        <Badge bg={PaymentMethodService.isValidPaymentMethod(code) ? 'success' : 'danger'}>
                          {PaymentMethodService.isValidPaymentMethod(code) ? 'Yes' : 'No'}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default PaymentMethodExample;
