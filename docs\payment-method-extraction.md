# Payment Method Extraction Documentation

## Overview

This document describes the extracted payment method functionality that was refactored from the invoice (facture) component into reusable services and utilities.

## Architecture

### 1. PaymentMethodService (`src/services/paymentMethodService.js`)

The main service class that handles all payment method operations:

```javascript
import { PaymentMethodService } from '../services/paymentMethodService';

// Get formatted display name
const displayName = PaymentMethodService.getDisplayName('carte_bancaire');
// Returns: "Carte Bancaire"

// Get badge variant for styling
const variant = PaymentMethodService.getBadgeVariant('paypal');
// Returns: "info"

// Check if payment method is valid
const isValid = PaymentMethodService.isValidPaymentMethod('carte');
// Returns: true

// Get icon component
const IconComponent = PaymentMethodService.getIcon('paypal');
// Returns: FaPaypal component

// Get all available payment methods
const methods = PaymentMethodService.getAvailablePaymentMethods();
```

### 2. PaymentMethodUtils (`src/utils/paymentMethodUtils.js`)

Utility functions for common payment method operations:

```javascript
import { 
  getPaymentMethodStatistics,
  filterInvoicesByPaymentMethod,
  getUniquePaymentMethods,
  validatePaymentMethod
} from '../utils/paymentMethodUtils';

// Get statistics from invoice data
const stats = getPaymentMethodStatistics(invoices);

// Filter invoices by payment method
const filtered = filterInvoicesByPaymentMethod(invoices, 'carte');

// Get unique payment methods from invoices
const unique = getUniquePaymentMethods(invoices);

// Validate payment data
const validation = validatePaymentMethod(paymentData);
```

### 3. PaymentMethodBadge Component (`src/components/PaymentMethodBadge.jsx`)

Reusable React component for displaying payment method badges:

```jsx
import PaymentMethodBadge from '../components/PaymentMethodBadge';

// Basic usage
<PaymentMethodBadge methodCode="carte" />

// With custom size
<PaymentMethodBadge methodCode="paypal" size={16} />

// Without icon
<PaymentMethodBadge methodCode="virement" showIcon={false} />
```

## API Integration

### Data Source: `/api/commandes`

The payment method data now comes exclusively from the `/api/commandes` endpoint:

```javascript
// Fetch orders with payment information
const response = await fetch(`${API_URL}/commandes?with=user,client,produits,paiement`);
```

### Data Structure

Payment method information is extracted from:

1. **Order.paiement.methode** - Primary source from payment relationship
2. **Order.paiement.methode_paiement** - Alternative field name
3. **Order.methode_paiement** - Fallback from order itself

```javascript
// Payment method extraction logic
const methode_paiement = 
  order.paiement?.methode || 
  order.paiement?.methode_paiement || 
  order.methode_paiement || 
  'Non spécifiée';
```

## Supported Payment Methods

| Code | Display Name | Badge Color | Icon |
|------|-------------|-------------|------|
| `carte` | Carte | Primary | Credit Card |
| `carte_bancaire` | Carte Bancaire | Primary | Credit Card |
| `paypal` | PayPal | Info | PayPal |
| `virement` | Virement Bancaire | Success | University |
| `especes` | Espèces | Warning | Money Bill |
| `cheque` | Chèque | Secondary | Money Bill |
| `mobile_money` | Mobile Money | Dark | Mobile |
| `stripe` | Stripe | Primary | Credit Card |
| `cash_on_delivery` | Paiement à la Livraison | Warning | Money Bill |
| `bank_transfer` | Virement Bancaire | Success | University |
| `gift_card` | Carte Cadeau | Danger | Gift |

## Usage Examples

### In Invoice Component

```jsx
// Before (inline logic)
<Badge bg="secondary" className="text-capitalize">
  {invoice.methode_paiement?.replace('_', ' ') || 'Non spécifiée'}
</Badge>

// After (using extracted service)
<PaymentMethodBadge methodCode={invoice.methode_paiement} />
```

### Statistics and Analytics

```javascript
// Get payment method statistics
const stats = PaymentMethodService.getPaymentMethodStats(invoices);

console.log(stats);
// Output:
// {
//   byMethod: {
//     carte: { count: 15, totalAmount: 2500.50, percentage: 60.0 },
//     paypal: { count: 8, totalAmount: 1200.25, percentage: 32.0 },
//     // ...
//   },
//   totalPayments: 25,
//   methodCount: 4
// }
```

### Filtering and Searching

```javascript
// Filter invoices by payment method
const carteInvoices = filterInvoicesByPaymentMethod(invoices, 'carte');

// Get unique payment methods from data
const uniqueMethods = getUniquePaymentMethods(invoices);

// Sort by preference
const sortedMethods = sortPaymentMethodsByPreference(uniqueMethods);
```

## Benefits of Extraction

1. **Reusability**: Payment method logic can be used across multiple components
2. **Consistency**: Uniform display and formatting throughout the application
3. **Maintainability**: Centralized logic makes updates easier
4. **Extensibility**: Easy to add new payment methods or modify existing ones
5. **Testing**: Isolated services are easier to unit test
6. **Performance**: Reduced code duplication and optimized rendering

## Migration Notes

### Updated Invoice Component

The `Invoices.jsx` component now:
- Fetches data only from `/api/commandes` endpoint
- Uses `PaymentMethodService` for all payment method operations
- Renders payment methods using the `PaymentMethodBadge` component
- Maintains backward compatibility with existing data structures

### Breaking Changes

None. The extraction maintains full backward compatibility while providing enhanced functionality.

## Testing

See `src/examples/PaymentMethodExample.jsx` for a comprehensive demonstration of all extracted functionality.

## Future Enhancements

1. Add payment method validation rules
2. Implement payment method preferences per user
3. Add support for custom payment method icons
4. Integrate with payment gateway APIs for real-time validation
5. Add payment method analytics dashboard
