import { PaymentMethodService } from '../services/paymentMethodService';

/**
 * Utility functions for payment method operations
 */

/**
 * Format payment method for display in React components
 * @param {string} methodCode - Payment method code
 * @param {Object} options - Display options
 * @returns {JSX.Element} - Formatted payment method component
 */
export const formatPaymentMethodDisplay = (methodCode, options = {}) => {
  const {
    showIcon = true,
    showBadge = true,
    iconSize = 12,
    className = ''
  } = options;

  const paymentMethod = PaymentMethodService.formatPaymentMethod(methodCode);
  
  if (!showBadge) {
    return paymentMethod.displayName;
  }

  const IconComponent = paymentMethod.icon;
  
  return {
    displayName: paymentMethod.displayName,
    badgeVariant: paymentMethod.badgeVariant,
    icon: showIcon ? IconComponent : null,
    iconSize,
    className: `text-capitalize ${className}`.trim()
  };
};

/**
 * Get payment method statistics for dashboard/reports
 * @param {Array} invoices - Array of invoice objects
 * @returns {Object} - Payment method statistics
 */
export const getPaymentMethodStatistics = (invoices) => {
  return PaymentMethodService.getPaymentMethodStats(invoices);
};

/**
 * Filter invoices by payment method
 * @param {Array} invoices - Array of invoice objects
 * @param {string} methodCode - Payment method code to filter by
 * @returns {Array} - Filtered invoices
 */
export const filterInvoicesByPaymentMethod = (invoices, methodCode) => {
  if (!methodCode || methodCode === 'all') {
    return invoices;
  }

  return invoices.filter(invoice => {
    const invoiceMethod = (invoice.methode_paiement || '').toLowerCase().trim();
    const filterMethod = methodCode.toLowerCase().trim();
    return invoiceMethod === filterMethod;
  });
};

/**
 * Get unique payment methods from a list of invoices
 * @param {Array} invoices - Array of invoice objects
 * @returns {Array} - Array of unique payment method objects
 */
export const getUniquePaymentMethods = (invoices) => {
  const uniqueMethods = new Set();
  
  invoices.forEach(invoice => {
    const method = invoice.methode_paiement || invoice.methode;
    if (method) {
      uniqueMethods.add(method.toLowerCase().trim());
    }
  });

  return Array.from(uniqueMethods).map(methodCode => ({
    code: methodCode,
    displayName: PaymentMethodService.getDisplayName(methodCode),
    badgeVariant: PaymentMethodService.getBadgeVariant(methodCode),
    icon: PaymentMethodService.getIcon(methodCode)
  }));
};

/**
 * Validate payment method data
 * @param {Object} paymentData - Payment data object
 * @returns {Object} - Validation result
 */
export const validatePaymentMethod = (paymentData) => {
  const errors = [];
  const warnings = [];

  if (!paymentData.methode_paiement && !paymentData.methode) {
    errors.push('Méthode de paiement manquante');
  }

  const methodCode = paymentData.methode_paiement || paymentData.methode;
  if (methodCode && !PaymentMethodService.isValidPaymentMethod(methodCode)) {
    warnings.push(`Méthode de paiement non reconnue: ${methodCode}`);
  }

  if (!paymentData.montant && !paymentData.amount) {
    errors.push('Montant du paiement manquant');
  }

  const amount = parseFloat(paymentData.montant || paymentData.amount || 0);
  if (amount <= 0) {
    errors.push('Le montant du paiement doit être supérieur à zéro');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    hasWarnings: warnings.length > 0
  };
};

/**
 * Sort payment methods by preference/popularity
 * @param {Array} paymentMethods - Array of payment method objects
 * @returns {Array} - Sorted payment methods
 */
export const sortPaymentMethodsByPreference = (paymentMethods) => {
  const preferenceOrder = [
    PaymentMethodService.PAYMENT_METHODS.CARTE,
    PaymentMethodService.PAYMENT_METHODS.CARTE_BANCAIRE,
    PaymentMethodService.PAYMENT_METHODS.STRIPE,
    PaymentMethodService.PAYMENT_METHODS.PAYPAL,
    PaymentMethodService.PAYMENT_METHODS.VIREMENT,
    PaymentMethodService.PAYMENT_METHODS.CASH_ON_DELIVERY,
    PaymentMethodService.PAYMENT_METHODS.MOBILE_MONEY,
    PaymentMethodService.PAYMENT_METHODS.ESPECES,
    PaymentMethodService.PAYMENT_METHODS.CHEQUE,
    PaymentMethodService.PAYMENT_METHODS.GIFT_CARD
  ];

  return paymentMethods.sort((a, b) => {
    const aIndex = preferenceOrder.indexOf(a.code);
    const bIndex = preferenceOrder.indexOf(b.code);
    
    // If both methods are in preference order, sort by preference
    if (aIndex !== -1 && bIndex !== -1) {
      return aIndex - bIndex;
    }
    
    // If only one is in preference order, prioritize it
    if (aIndex !== -1) return -1;
    if (bIndex !== -1) return 1;
    
    // If neither is in preference order, sort alphabetically
    return a.displayName.localeCompare(b.displayName);
  });
};

/**
 * Get payment method color for charts/visualizations
 * @param {string} methodCode - Payment method code
 * @returns {string} - Color hex code
 */
export const getPaymentMethodColor = (methodCode) => {
  const colorMap = {
    [PaymentMethodService.PAYMENT_METHODS.CARTE]: '#0d6efd',
    [PaymentMethodService.PAYMENT_METHODS.CARTE_BANCAIRE]: '#0d6efd',
    [PaymentMethodService.PAYMENT_METHODS.PAYPAL]: '#17a2b8',
    [PaymentMethodService.PAYMENT_METHODS.VIREMENT]: '#198754',
    [PaymentMethodService.PAYMENT_METHODS.ESPECES]: '#ffc107',
    [PaymentMethodService.PAYMENT_METHODS.CHEQUE]: '#6c757d',
    [PaymentMethodService.PAYMENT_METHODS.MOBILE_MONEY]: '#212529',
    [PaymentMethodService.PAYMENT_METHODS.STRIPE]: '#0d6efd',
    [PaymentMethodService.PAYMENT_METHODS.CASH_ON_DELIVERY]: '#ffc107',
    [PaymentMethodService.PAYMENT_METHODS.BANK_TRANSFER]: '#198754',
    [PaymentMethodService.PAYMENT_METHODS.GIFT_CARD]: '#dc3545'
  };

  const normalizedCode = methodCode?.toLowerCase().trim();
  return colorMap[normalizedCode] || '#6c757d';
};

/**
 * Export all payment method constants for easy access
 */
export const PAYMENT_METHODS = PaymentMethodService.PAYMENT_METHODS;
export const PAYMENT_METHOD_NAMES = PaymentMethodService.PAYMENT_METHOD_NAMES;
export const PAYMENT_METHOD_BADGES = PaymentMethodService.PAYMENT_METHOD_BADGES;
export const PAYMENT_METHOD_ICONS = PaymentMethodService.PAYMENT_METHOD_ICONS;

export default {
  formatPaymentMethodDisplay,
  getPaymentMethodStatistics,
  filterInvoicesByPaymentMethod,
  getUniquePaymentMethods,
  validatePaymentMethod,
  sortPaymentMethodsByPreference,
  getPaymentMethodColor,
  PAYMENT_METHODS,
  PAYMENT_METHOD_NAMES,
  PAYMENT_METHOD_BADGES,
  PAYMENT_METHOD_ICONS
};
