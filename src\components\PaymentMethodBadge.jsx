import React from 'react';
import { Badge } from 'react-bootstrap';
import { PaymentMethodService } from '../services/paymentMethodService';

/**
 * Reusable component for displaying payment method badges
 */
const PaymentMethodBadge = ({ 
  methodCode, 
  size = 12, 
  className = '',
  showIcon = true 
}) => {
  const paymentMethod = PaymentMethodService.formatPaymentMethod(methodCode);
  const IconComponent = paymentMethod.icon;

  return (
    <Badge 
      bg={paymentMethod.badgeVariant} 
      className={`text-capitalize d-flex align-items-center gap-1 ${className}`.trim()}
    >
      {showIcon && <IconComponent size={size} />}
      {paymentMethod.displayName}
    </Badge>
  );
};

export default PaymentMethodBadge;
