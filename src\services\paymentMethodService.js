import { 
  FaCreditCard, 
  FaPaypal, 
  FaMoneyBillWave, 
  FaMobileAlt, 
  FaUniversity,
  FaGift,
  FaQuestionCircle
} from 'react-icons/fa';

/**
 * Service for handling payment method operations
 */
export class PaymentMethodService {
  
  /**
   * Payment method constants
   */
  static PAYMENT_METHODS = {
    CARTE: 'carte',
    CARTE_BANCAIRE: 'carte_bancaire',
    PAYPAL: 'paypal',
    VIREMENT: 'virement',
    ESPECES: 'especes',
    CHEQUE: 'cheque',
    MOBILE_MONEY: 'mobile_money',
    STRIPE: 'stripe',
    CASH_ON_DELIVERY: 'cash_on_delivery',
    BANK_TRANSFER: 'bank_transfer',
    GIFT_CARD: 'gift_card'
  };

  /**
   * Payment method display names mapping
   */
  static PAYMENT_METHOD_NAMES = {
    [this.PAYMENT_METHODS.CARTE]: 'Carte',
    [this.PAYMENT_METHODS.CARTE_BANCAIRE]: 'Carte Bancaire',
    [this.PAYMENT_METHODS.PAYPAL]: 'PayPal',
    [this.PAYMENT_METHODS.VIREMENT]: 'Virement Bancaire',
    [this.PAYMENT_METHODS.ESPECES]: 'Espèces',
    [this.PAYMENT_METHODS.CHEQUE]: 'Chèque',
    [this.PAYMENT_METHODS.MOBILE_MONEY]: 'Mobile Money',
    [this.PAYMENT_METHODS.STRIPE]: 'Stripe',
    [this.PAYMENT_METHODS.CASH_ON_DELIVERY]: 'Paiement à la Livraison',
    [this.PAYMENT_METHODS.BANK_TRANSFER]: 'Virement Bancaire',
    [this.PAYMENT_METHODS.GIFT_CARD]: 'Carte Cadeau'
  };

  /**
   * Payment method badge variants mapping
   */
  static PAYMENT_METHOD_BADGES = {
    [this.PAYMENT_METHODS.CARTE]: 'primary',
    [this.PAYMENT_METHODS.CARTE_BANCAIRE]: 'primary',
    [this.PAYMENT_METHODS.PAYPAL]: 'info',
    [this.PAYMENT_METHODS.VIREMENT]: 'success',
    [this.PAYMENT_METHODS.ESPECES]: 'warning',
    [this.PAYMENT_METHODS.CHEQUE]: 'secondary',
    [this.PAYMENT_METHODS.MOBILE_MONEY]: 'dark',
    [this.PAYMENT_METHODS.STRIPE]: 'primary',
    [this.PAYMENT_METHODS.CASH_ON_DELIVERY]: 'warning',
    [this.PAYMENT_METHODS.BANK_TRANSFER]: 'success',
    [this.PAYMENT_METHODS.GIFT_CARD]: 'danger'
  };

  /**
   * Payment method icons mapping
   */
  static PAYMENT_METHOD_ICONS = {
    [this.PAYMENT_METHODS.CARTE]: FaCreditCard,
    [this.PAYMENT_METHODS.CARTE_BANCAIRE]: FaCreditCard,
    [this.PAYMENT_METHODS.PAYPAL]: FaPaypal,
    [this.PAYMENT_METHODS.VIREMENT]: FaUniversity,
    [this.PAYMENT_METHODS.ESPECES]: FaMoneyBillWave,
    [this.PAYMENT_METHODS.CHEQUE]: FaMoneyBillWave,
    [this.PAYMENT_METHODS.MOBILE_MONEY]: FaMobileAlt,
    [this.PAYMENT_METHODS.STRIPE]: FaCreditCard,
    [this.PAYMENT_METHODS.CASH_ON_DELIVERY]: FaMoneyBillWave,
    [this.PAYMENT_METHODS.BANK_TRANSFER]: FaUniversity,
    [this.PAYMENT_METHODS.GIFT_CARD]: FaGift
  };

  /**
   * Get formatted display name for a payment method
   * @param {string} methodCode - The payment method code
   * @returns {string} - Formatted display name
   */
  static getDisplayName(methodCode) {
    if (!methodCode) return 'Non spécifiée';
    
    // Normalize the method code
    const normalizedCode = methodCode.toLowerCase().trim();
    
    // Check if we have a direct mapping
    if (this.PAYMENT_METHOD_NAMES[normalizedCode]) {
      return this.PAYMENT_METHOD_NAMES[normalizedCode];
    }
    
    // Fallback: capitalize and replace underscores with spaces
    return methodCode
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Get badge variant for a payment method
   * @param {string} methodCode - The payment method code
   * @returns {string} - Bootstrap badge variant
   */
  static getBadgeVariant(methodCode) {
    if (!methodCode) return 'secondary';
    
    const normalizedCode = methodCode.toLowerCase().trim();
    return this.PAYMENT_METHOD_BADGES[normalizedCode] || 'secondary';
  }

  /**
   * Get icon component for a payment method
   * @param {string} methodCode - The payment method code
   * @returns {React.Component} - Icon component
   */
  static getIcon(methodCode) {
    if (!methodCode) return FaQuestionCircle;
    
    const normalizedCode = methodCode.toLowerCase().trim();
    return this.PAYMENT_METHOD_ICONS[normalizedCode] || FaQuestionCircle;
  }

  /**
   * Get all available payment methods
   * @returns {Array} - Array of payment method objects
   */
  static getAvailablePaymentMethods() {
    return Object.entries(this.PAYMENT_METHOD_NAMES).map(([code, name]) => ({
      code,
      name,
      badgeVariant: this.getBadgeVariant(code),
      icon: this.getIcon(code)
    }));
  }

  /**
   * Validate if a payment method is valid
   * @param {string} methodCode - The payment method code
   * @returns {boolean} - True if valid
   */
  static isValidPaymentMethod(methodCode) {
    if (!methodCode) return false;
    
    const normalizedCode = methodCode.toLowerCase().trim();
    return Object.values(this.PAYMENT_METHODS).includes(normalizedCode);
  }

  /**
   * Format payment method for display with icon and badge
   * @param {string} methodCode - The payment method code
   * @param {Object} options - Display options
   * @returns {Object} - Formatted payment method object
   */
  static formatPaymentMethod(methodCode, options = {}) {
    const {
      showIcon = true,
      showBadge = true,
      badgeSize = 'sm'
    } = options;

    return {
      code: methodCode,
      displayName: this.getDisplayName(methodCode),
      badgeVariant: this.getBadgeVariant(methodCode),
      icon: showIcon ? this.getIcon(methodCode) : null,
      showBadge,
      badgeSize,
      isValid: this.isValidPaymentMethod(methodCode)
    };
  }

  /**
   * Get payment method statistics
   * @param {Array} payments - Array of payment objects
   * @returns {Object} - Payment method statistics
   */
  static getPaymentMethodStats(payments) {
    const stats = {};
    let total = 0;

    payments.forEach(payment => {
      const method = payment.methode_paiement || payment.methode || 'unknown';
      const normalizedMethod = method.toLowerCase().trim();
      const displayName = this.getDisplayName(normalizedMethod);
      
      if (!stats[normalizedMethod]) {
        stats[normalizedMethod] = {
          code: normalizedMethod,
          displayName,
          count: 0,
          totalAmount: 0,
          badgeVariant: this.getBadgeVariant(normalizedMethod),
          icon: this.getIcon(normalizedMethod)
        };
      }
      
      stats[normalizedMethod].count++;
      stats[normalizedMethod].totalAmount += parseFloat(payment.montant || payment.amount || 0);
      total++;
    });

    // Calculate percentages
    Object.values(stats).forEach(stat => {
      stat.percentage = total > 0 ? ((stat.count / total) * 100).toFixed(1) : 0;
    });

    return {
      byMethod: stats,
      totalPayments: total,
      methodCount: Object.keys(stats).length
    };
  }
}

export default PaymentMethodService;
